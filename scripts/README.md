# Scripts Directory

This directory contains utility scripts for the Calmren business management system.

## 📄 Active Scripts

### `statement_generator.py`
**Purpose**: Generates monthly statements from database records
**Features**:
- Monthly summary of all invoices and work
- Project breakdown by hours and amount
- Professional formatting for client records

**Usage**:
```bash
cd scripts
python3 statement_generator.py
```

**Input**: Database records from `data/calmren.db`
**Output**: `../statements/STMT-YYYY-MM_Statement.html`

## 🗂️ Invoice Generation

**Note**: Invoice generation is now handled by the database-based system in `python-service/invoice_generator.py`.
The old CSV-based invoice generator has been moved to `archive/legacy-invoice-generators/` for reference.

### `statement_generator.py`
**Purpose**: Generates monthly statements summarizing all invoices
**Features**:
- Monthly summary of all invoices
- Professional HTML formatting
- Payment status tracking
- Total amounts and outstanding balances

**Usage**:
```bash
cd scripts
python3 statement_generator.py
```

**Input**: Invoice data and time logs
**Output**: `../statements/STMT-YYYY-MM_Statement.html`

## 🔗 Integration

These scripts are integrated with the main application through:

1. **Python Service** (`python-service/` directory) - Docker containerized versions
2. **API Routes** (`api/src/routes/python-bridge.js`) - REST API endpoints
3. **Frontend** - Web interface for triggering generation

## 📊 Project Rates

Current hourly rates configured in `invoice_generator.py`:
- **IT Admin**: $80.00/hour
- **IT Continuity**: $80.00/hour  
- **Media**: $50.00/hour
- **MFA Setup**: $80.00/hour
- **Admin Overview**: $80.00/hour
- **General Support**: $80.00/hour
- **Default**: $80.00/hour

## 🔄 Workflow

1. **Time Tracking** → Log hours in web application
2. **CSV Export** → Time data exported to `Calmren - Time Log.csv`
3. **Invoice Generation** → Run `invoice_generator.py` to create HTML invoices
4. **Statement Generation** → Run `statement_generator.py` for monthly summaries
5. **File Management** → Generated files stored in `invoices/` and `statements/` directories

## ⚠️ Important Notes

- These are the **current, active versions** with latest enhancements
- The `python-service/` directory contains Docker versions that may be older
- Always run scripts from the `scripts/` directory to ensure correct file paths
- Generated files are automatically saved to the appropriate output directories

## 📅 Last Updated
September 22, 2025
