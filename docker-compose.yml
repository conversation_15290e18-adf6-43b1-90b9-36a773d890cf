
services:
  # Frontend (Vue.js)
  frontend:
    build: .
    ports:
      - "80:80"
    depends_on:
      - api
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # Backend API (Node.js/Express)
  api:
    build: ./api
    depends_on:
      - python-service
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=sqlite:///app/data/calmren.db
      - PYTHON_SERVICE_URL=http://python-service:8000
    volumes:
      - ./data:/app/data
      - ./invoices:/app/invoices
      - ./statements:/app/statements

    restart: unless-stopped

  # Python Service (Invoice/Statement Generation)
  python-service:
    build: ./python-service
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data:/app/data
      - ./invoices:/app/invoices
      - ./statements:/app/statements

    restart: unless-stopped

  # Development services (only for development)
  frontend-dev:
    build:
      context: .
      target: build-stage
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
    depends_on:
      - api
    profiles:
      - dev

  api-dev:
    build: ./api
    ports:
      - "3001:3001"
    depends_on:
      - python-service-dev
    volumes:
      - ./api:/app
      - /app/node_modules
      - ./data:/app/data
      - ./invoices:/app/invoices
      - ./statements:/app/statements

    command: npm run dev
    environment:
      - NODE_ENV=development
      - PORT=3001
      - PYTHON_SERVICE_URL=http://python-service-dev:8000
    profiles:
      - dev

  python-service-dev:
    build: ./python-service
    ports:
      - "8000:8000"
    volumes:
      - ./python-service:/app
      - ./data:/app/data
      - ./invoices:/app/invoices
      - ./statements:/app/statements

    command: python service.py
    environment:
      - PYTHONUNBUFFERED=1
    profiles:
      - dev

  # Cloudflare Tunnel (exposes port 80 to the internet via Cloudflare)
  cloudflared:
    image: cloudflare/cloudflared:latest
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    depends_on:
      - frontend
    restart: unless-stopped

volumes:
  data:


