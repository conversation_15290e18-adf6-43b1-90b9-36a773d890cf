#!/usr/bin/env python3
"""
Calmren Weekly Invoice Generator
Generates professional invoices and task breakdowns from time log data
"""

import pandas as pd
import datetime
from datetime import timedelta
import os
from pathlib import Path

# Your business details
BUSINESS_INFO = {
    'name': '<PERSON><PERSON>',
    'email': '<EMAIL>',
    'address': '418a Kaitemako Rd',
    'city': 'Welcome Bay, 3175',
    'phone': '**********',
    'hourly_rate': 80.00,
    'bank_account': '04-2021-0224109-11'
}

# Client details
CLIENT_INFO = {
    'name': 'Free Speech Union (NZ) Incorporated',
    'website': 'fsu.nz'
}

def parse_date(date_str):
    """Parse date string in DD/MM/YYYY format"""
    try:
        return datetime.datetime.strptime(date_str, '%d/%m/%Y').date()
    except:
        return None

def parse_date_iso(date_str):
    """Parse date string in YYYY-MM-DD format (ISO format from database)"""
    try:
        return datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
    except:
        return None

def get_week_dates(date):
    """Get Monday and Sunday for the week containing the given date"""
    # Find Monday of the week
    monday = date - timedelta(days=date.weekday())
    sunday = monday + timedelta(days=6)
    return monday, sunday

def load_time_data():
    """Load and process time log data from database"""
    print("📊 Loading time log data from database...")

    import sqlite3

    # Connect to database
    db_path = 'data/calmren.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # This enables column access by name

    try:
        cursor = conn.cursor()

        # Get time entries with client and project info
        cursor.execute("""
            SELECT
                te.date,
                c.name as client_name,
                p.name as project_name,
                te.start_time,
                te.end_time,
                te.duration,
                te.description,
                te.hourly_rate,
                te.amount
            FROM time_entries te
            LEFT JOIN clients c ON te.client_id = c.id
            LEFT JOIN projects p ON te.project_id = p.id
            WHERE te.status = 'logged'
            ORDER BY te.date, te.created_at
        """)

        rows = cursor.fetchall()
        data = []

        for row in rows:
            # Convert database row to format expected by the rest of the code
            # Parse date from YYYY-MM-DD format
            date_obj = parse_date_iso(row['date'])
            if date_obj is None:
                continue

            # Format data to match expected structure
            data.append({
                'Date': row['date'],
                'Client': row['client_name'] or 'Unknown Client',
                'Project': row['project_name'] or 'General',
                'Start Time': row['start_time'] or '',
                'End Time': row['end_time'] or '',
                'Duration': float(row['duration']) if row['duration'] else 0.0,
                'Task Description': row['description'] or '',
                'parsed_date': date_obj,
                'hourly_rate': float(row['hourly_rate']) if row['hourly_rate'] else 80.0,
                'amount': float(row['amount']) if row['amount'] else 0.0
            })

    finally:
        conn.close()

    # Create DataFrame
    df = pd.DataFrame(data)

    # Filter out entries with zero duration
    df = df[df['Duration'] > 0]

    print(f"✅ Loaded {len(df)} time entries from database")
    return df

def get_weekly_data(df):
    """Group data by weeks (Monday-Sunday)"""
    weeks = {}
    
    for _, row in df.iterrows():
        date = row['parsed_date']
        monday, sunday = get_week_dates(date)
        
        week_key = f"{monday.strftime('%Y-%m-%d')} to {sunday.strftime('%Y-%m-%d')}"
        
        if week_key not in weeks:
            weeks[week_key] = {
                'monday': monday,
                'sunday': sunday,
                'entries': [],
                'total_hours': 0,
                'projects': {}
            }
        
        weeks[week_key]['entries'].append(row)
        weeks[week_key]['total_hours'] += row['Duration']
        
        # Group by project
        project = row['Project']
        if project not in weeks[week_key]['projects']:
            weeks[week_key]['projects'][project] = {
                'hours': 0,
                'tasks': []
            }
        
        weeks[week_key]['projects'][project]['hours'] += row['Duration']
        weeks[week_key]['projects'][project]['tasks'].append(row)
    
    return weeks

def generate_combined_invoice_html(week_data, week_key, invoice_number):
    """Generate combined invoice and task breakdown HTML"""
    
    monday = week_data['monday']
    sunday = week_data['sunday']
    total_hours = week_data['total_hours']
    total_amount = total_hours * BUSINESS_INFO['hourly_rate']
    
    # Group projects for invoice
    project_summary = []
    for project, data in week_data['projects'].items():
        hours = data['hours']
        amount = hours * BUSINESS_INFO['hourly_rate']
        
        # Create brief project summary for main invoice
        task_summary = f"{project} services as detailed in attached breakdown"
        
        project_summary.append({
            'name': project,
            'hours': hours,
            'amount': amount,
            'tasks': task_summary
        })
    
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {invoice_number}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.4;
            color: #333;
        }}
        
        .invoice-header {{
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }}
        
        .invoice-title {{
            font-size: 2.5em;
            font-weight: bold;
            color: #2c5aa0;
            margin: 0;
        }}
        
        .invoice-details {{
            text-align: right;
            font-size: 0.9em;
        }}
        
        .business-details {{
            margin-bottom: 30px;
        }}
        
        .business-name {{
            font-size: 1.4em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }}
        
        .client-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }}
        
        .section-title {{
            font-size: 1.1em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }}
        
        .invoice-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .invoice-table th {{
            background-color: #2c5aa0;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }}
        
        .invoice-table td {{
            padding: 12px;
            border-bottom: 1px solid #ddd;
        }}
        
        .invoice-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .total-row {{
            background-color: #e3f2fd !important;
            font-weight: bold;
            border-top: 2px solid #2c5aa0;
        }}
        
        .amount-cell {{
            text-align: right;
            font-family: 'Courier New', monospace;
        }}
        
        .total-amount {{
            font-size: 1.2em;
            color: #2c5aa0;
        }}
        
        .payment-terms {{
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 30px 0;
        }}
        
        .footer {{
            margin-top: 40px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }}
        
        .highlight-box {{
            background-color: #e8f4f8;
            border: 2px solid #2c5aa0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 15px;
        }}

        .report-title {{
            font-size: 2.2em;
            color: #2c5aa0;
            margin-bottom: 10px;
        }}

        .period {{
            font-size: 1.1em;
            color: #666;
        }}

        .summary-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}

        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c5aa0;
        }}

        .stat-label {{
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }}

        .day-section {{
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }}

        .day-header {{
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }}

        .day-summary {{
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            font-size: 0.95em;
        }}

        .task-list {{
            background-color: white;
        }}

        .task-item {{
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }}

        .task-item:last-child {{
            border-bottom: none;
        }}

        .task-time {{
            font-weight: bold;
            color: #2c5aa0;
            min-width: 60px;
            font-family: 'Courier New', monospace;
        }}

        .task-project {{
            background-color: #e8f4f8;
            color: #2c5aa0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }}

        .task-description {{
            flex: 1;
            padding-left: 10px;
        }}

        .footer-note {{
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}

        @media print {{
            body {{ margin: 0; }}
            .invoice-header {{ page-break-inside: avoid; }}
            .day-section {{ page-break-inside: avoid; }}
        }}
    </style>
</head>
<body>
    <div class="invoice-header">
        <div>
            <h1 class="invoice-title">INVOICE</h1>
        </div>
        <div class="invoice-details">
            <strong>Invoice #:</strong> {invoice_number}<br>
            <strong>Date:</strong> {datetime.date.today().strftime('%d %B %Y')}<br>
            <strong>Period:</strong> Week ending {sunday.strftime('%d %B %Y')}
        </div>
    </div>

    <div class="business-details">
        <div class="business-name">{BUSINESS_INFO['name']}</div>
        <div>{BUSINESS_INFO['address']}</div>
        <div>{BUSINESS_INFO['city']}</div>
        <div>Email: {BUSINESS_INFO['email']}</div>
        <div>Phone: {BUSINESS_INFO['phone']}</div>
    </div>

    <div class="client-section">
        <div class="section-title">Bill To:</div>
        <div><strong>{CLIENT_INFO['name']}</strong></div>
        <div>{CLIENT_INFO['website']}</div>
    </div>

    <div class="highlight-box">
        <strong>Services Period:</strong> {monday.strftime('%d %B %Y')} – {sunday.strftime('%d %B %Y')}<br>
        <strong>Total Hours:</strong> {total_hours:.1f} hours
    </div>

    <table class="invoice-table">
        <thead>
            <tr>
                <th>Description</th>
                <th style="width: 100px;">Hours</th>
                <th style="width: 100px;">Rate</th>
                <th style="width: 120px;">Amount</th>
            </tr>
        </thead>
        <tbody>"""
    
    # Add project rows
    for project in project_summary:
        html += f"""
            <tr>
                <td>{project['name']}<br>
                    <small style="color: #666;">{project['tasks']}</small>
                </td>
                <td class="amount-cell">{project['hours']:.1f}</td>
                <td class="amount-cell">${BUSINESS_INFO['hourly_rate']:.2f}</td>
                <td class="amount-cell">${project['amount']:.2f}</td>
            </tr>"""
    
    html += f"""
            <tr class="total-row">
                <td colspan="3" style="text-align: right;"><strong>TOTAL (GST Exempt):</strong></td>
                <td class="amount-cell total-amount"><strong>${total_amount:.2f}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="payment-terms">
        <div class="section-title">Payment Terms</div>
        <p><strong>Payment Due:</strong> Net 14 days from invoice date</p>
        <p><strong>Payment Method:</strong> Bank transfer preferred</p>
        <p><strong>Bank Details:</strong><br>
        Account Name: {BUSINESS_INFO['name']}<br>
        Account Number: {BUSINESS_INFO['bank_account']}</p>
    </div>

    <div class="highlight-box">
        <strong>Note:</strong> This invoice is GST exempt. Detailed task breakdown follows below.
    </div>

    <!-- PAGE BREAK FOR PRINT -->
    <div style="page-break-before: always; margin-top: 50px;">

        <!-- TASK BREAKDOWN SECTION -->
        <div class="header">
            <h1 class="report-title">DETAILED TASK BREAKDOWN</h1>
            <div style="font-size: 1.0em; color: #2c5aa0; margin-bottom: 5px;">
                <strong>Invoice:</strong> {invoice_number}
            </div>
            <div class="period">Week Ending {sunday.strftime('%d %B %Y')} • FSU Client Services</div>
        </div>

        <div class="summary-box">
            <h2 style="margin-top: 0; color: #2c5aa0;">Weekly Summary</h2>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value">{total_hours:.1f}</div>
                    <div class="stat-label">Total Hours</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{len(week_data['entries'])}</div>
                    <div class="stat-label">Task Entries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{len(week_data['projects'])}</div>
                    <div class="stat-label">Active Projects</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${total_amount:.2f}</div>
                    <div class="stat-label">Total Value</div>
                </div>
            </div>
        </div>"""

    # Add daily breakdown
    daily_entries = {}
    for entry in week_data['entries']:
        date = entry['parsed_date']
        if date not in daily_entries:
            daily_entries[date] = []
        daily_entries[date].append(entry)

    for date in sorted(daily_entries.keys()):
        entries = daily_entries[date]
        daily_hours = sum(entry['Duration'] for entry in entries)

        day_name = date.strftime('%A, %d %B %Y')

        html += f"""
        <div class="day-section">
            <div class="day-header">{day_name}</div>
            <div class="day-summary"><strong>Daily Total:</strong> {daily_hours:.1f} hours</div>
            <div class="task-list">"""

        for entry in entries:
            html += f"""
                <div class="task-item">
                    <div class="task-time">{entry['Duration']:.1f}h</div>
                    <div class="task-project">{entry['Project']}</div>
                    <div class="task-description">{entry['Task Description']}</div>
                </div>"""

        html += """
            </div>
        </div>"""

    html += f"""
        <div class="footer-note">
            <p><strong>Report prepared:</strong> {datetime.date.today().strftime('%d %B %Y')}<br>
            <strong>Prepared by:</strong> {BUSINESS_INFO['name']}</p>
            <p><em>All times recorded via automated time tracking system</em></p>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business! For any queries regarding this invoice, please don't hesitate to contact me.</p>
        <p><em>Professional IT Services • Reliable • Efficient</em></p>
    </div>
</body>
</html>"""
    
    return html

def generate_task_breakdown_html(week_data, week_key, invoice_number):
    """Generate detailed task breakdown HTML"""

    monday = week_data['monday']
    sunday = week_data['sunday']
    total_hours = week_data['total_hours']
    total_amount = total_hours * BUSINESS_INFO['hourly_rate']
    
    # Group entries by date
    daily_entries = {}
    for entry in week_data['entries']:
        date = entry['parsed_date']
        if date not in daily_entries:
            daily_entries[date] = []
        daily_entries[date].append(entry)
    
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Breakdown - Week Ending {sunday.strftime('%d %B %Y')}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.5;
            color: #333;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 15px;
        }}
        
        .report-title {{
            font-size: 2.2em;
            color: #2c5aa0;
            margin-bottom: 10px;
        }}
        
        .period {{
            font-size: 1.1em;
            color: #666;
        }}
        
        .summary-box {{
            background-color: #e3f2fd;
            border-left: 5px solid #2c5aa0;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }}
        
        .summary-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        
        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c5aa0;
        }}
        
        .stat-label {{
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }}
        
        .day-section {{
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }}
        
        .day-header {{
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }}
        
        .day-summary {{
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            font-size: 0.95em;
        }}
        
        .task-list {{
            background-color: white;
        }}
        
        .task-item {{
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }}
        
        .task-item:last-child {{
            border-bottom: none;
        }}
        
        .task-time {{
            font-weight: bold;
            color: #2c5aa0;
            min-width: 60px;
            font-family: 'Courier New', monospace;
        }}
        
        .task-project {{
            background-color: #e8f4f8;
            color: #2c5aa0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }}
        
        .task-description {{
            flex: 1;
            padding-left: 10px;
        }}
        
        .footer-note {{
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1 class="report-title">DETAILED TASK BREAKDOWN</h1>
        <div class="period">Week Ending {sunday.strftime('%d %B %Y')} • FSU Client Services</div>
        <div style="margin-top: 10px; font-size: 1.0em; color: #2c5aa0;">
            <strong>Related Invoice:</strong> {invoice_number}
        </div>
    </div>

    <div class="summary-box">
        <h2 style="margin-top: 0; color: #2c5aa0;">Weekly Summary</h2>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-value">{total_hours:.1f}</div>
                <div class="stat-label">Total Hours</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{len(week_data['entries'])}</div>
                <div class="stat-label">Task Entries</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{len(week_data['projects'])}</div>
                <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${total_amount:.2f}</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>
    </div>"""
    
    # Add daily breakdown
    for date in sorted(daily_entries.keys()):
        entries = daily_entries[date]
        daily_hours = sum(entry['Duration'] for entry in entries)
        
        day_name = date.strftime('%A, %d %B %Y')
        
        html += f"""
    <div class="day-section">
        <div class="day-header">{day_name}</div>
        <div class="day-summary"><strong>Daily Total:</strong> {daily_hours:.1f} hours</div>
        <div class="task-list">"""
        
        for entry in entries:
            html += f"""
            <div class="task-item">
                <div class="task-time">{entry['Duration']:.1f}h</div>
                <div class="task-project">{entry['Project']}</div>
                <div class="task-description">{entry['Task Description']}</div>
            </div>"""
        
        html += """
        </div>
    </div>"""
    
    html += f"""
    <div class="footer-note">
        <p><strong>Report prepared:</strong> {datetime.date.today().strftime('%d %B %Y')}<br>
        <strong>Prepared by:</strong> {BUSINESS_INFO['name']}<br>
        <strong>Supporting Invoice:</strong> {invoice_number}</p>
        <p><em>All times recorded via automated time tracking system</em></p>
        <p style="margin-top: 10px; font-size: 0.85em; color: #888;">
            <strong>Verification:</strong> Total hours ({total_hours:.1f}h) and amount (${total_amount:.2f})
            match the corresponding invoice for accuracy and transparency.
        </p>
    </div>
</body>
</html>"""
    
    return html

def main():
    """Generate all weekly invoices"""
    print("🚀 Calmren Invoice Generator Starting...")
    
    # Load data
    df = load_time_data()
    
    # Group by weeks
    weeks = get_weekly_data(df)
    
    print(f"📅 Found {len(weeks)} weeks of data")
    
    # Create output directory
    output_dir = Path("invoices")
    output_dir.mkdir(exist_ok=True)
    
    # Generate invoices for each week
    invoice_counter = 1
    
    for week_key in sorted(weeks.keys()):
        week_data = weeks[week_key]
        
        # Generate invoice number
        year = week_data['sunday'].year
        invoice_number = f"INV-{year}-{invoice_counter:03d}"
        
        print(f"📄 Generating combined invoice {invoice_number} for {week_key}")

        # Generate combined invoice and task breakdown HTML
        combined_html = generate_combined_invoice_html(week_data, week_key, invoice_number)
        combined_file = output_dir / f"{invoice_number}_Complete.html"

        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(combined_html)

        print(f"   ✅ Created: {combined_file.name}")
        
        invoice_counter += 1
    
    print(f"\n🎉 Generated {len(weeks)} combined invoices in '{output_dir}' directory")
    print("\n📧 Email-ready files:")
    print("   • Each file contains both invoice and detailed task breakdown")
    print("   • Single document per week for easy client handling")
    print("   • Files are HTML format for easy email attachment")
    print("   • Can be printed to PDF if needed")
    print("   • Professional page breaks for clean printing")

if __name__ == "__main__":
    main()
