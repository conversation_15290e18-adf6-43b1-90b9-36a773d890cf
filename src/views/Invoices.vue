<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Invoices</h1>
      <p class="text-gray-600 mt-2">Generate and manage your invoices</p>
    </div>

    <!-- Generate Invoice -->
    <div class="card mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Generate New Invoice</h2>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
          <select v-model="newInvoice.client" class="input-field">
            <option value="">Select client</option>
            <option value="FSU">FSU</option>
            <option value="Client B">Client B</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Week Start</label>
          <input
            v-model="newInvoice.weekStart"
            type="date"
            class="input-field"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Week End</label>
          <input
            v-model="newInvoice.weekEnd"
            type="date"
            class="input-field"
          />
        </div>
        <div class="flex items-end">
          <button @click="generateInvoice" class="btn-primary w-full">
            Generate Invoice
          </button>
        </div>
      </div>
    </div>

    <!-- Invoice List -->
    <div class="card">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">Recent Invoices</h2>
        <div class="flex space-x-2">
          <button class="btn-secondary text-sm">Export All</button>
          <button class="btn-secondary text-sm">Filter</button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4 font-medium text-gray-700">Invoice #</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Client</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Period</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Hours</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Amount</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Created</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="invoice in invoices" :key="invoice.id" class="border-b border-gray-100 hover:bg-gray-50">
              <td class="py-3 px-4 text-sm font-medium">{{ invoice.invoice_number }}</td>
              <td class="py-3 px-4 text-sm">{{ invoice.client_name }}</td>
              <td class="py-3 px-4 text-sm">{{ formatPeriod(invoice.week_start, invoice.week_end) }}</td>
              <td class="py-3 px-4 text-sm">{{ invoice.total_hours }}h</td>
              <td class="py-3 px-4 text-sm font-medium">${{ invoice.total_amount.toFixed(2) }}</td>
              <td class="py-3 px-4 text-sm">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getStatusClass(invoice.status)">
                  {{ invoice.status_display || invoice.status }}
                </span>
              </td>
              <td class="py-3 px-4 text-sm">{{ formatDate(invoice.created_at) }}</td>
              <td class="py-3 px-4 text-sm">
                <div class="flex space-x-2">
                  <button @click="viewInvoice(invoice)" class="text-primary-600 hover:text-primary-700">View</button>
                  <button @click="downloadInvoice(invoice)" class="text-primary-600 hover:text-primary-700">Download</button>
                  <button @click="updatePayment(invoice)" class="text-primary-600 hover:text-primary-700">
                    {{ invoice.status === 'pending' ? 'Mark Paid' : 'Payment' }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="invoices.length === 0" class="text-center py-8 text-gray-500">
        No invoices yet. Generate your first invoice above!
      </div>
    </div>

    <!-- Invoice Preview Modal (placeholder) -->
    <div v-if="showPreview" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Invoice Preview</h3>
          <button @click="showPreview = false" class="text-gray-500 hover:text-gray-700">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        <div class="border border-gray-200 rounded-lg p-8">
          <p class="text-center text-gray-500">Invoice preview will be displayed here</p>
        </div>
        <div class="flex justify-end space-x-4 mt-6">
          <button @click="showPreview = false" class="btn-secondary">Close</button>
          <button class="btn-primary">Download PDF</button>
        </div>
      </div>
    </div>

    <!-- Payment Update Modal -->
    <div v-if="showPaymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Update Payment Status</h3>
          <button @click="showPaymentModal = false" class="text-gray-500 hover:text-gray-700">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <div v-if="selectedInvoice" class="mb-4 p-3 bg-gray-50 rounded">
          <div class="text-sm">
            <strong>{{ selectedInvoice.invoice_number }}</strong> - {{ selectedInvoice.client_name }}
          </div>
          <div class="text-sm text-gray-600">
            ${{ selectedInvoice.total_amount.toFixed(2) }} • {{ selectedInvoice.total_hours }}h
          </div>
        </div>

        <form @submit.prevent="savePaymentUpdate" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
            <select v-model="paymentForm.status" class="input-field">
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div v-if="paymentForm.status === 'paid'">
            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
            <input
              v-model="paymentForm.payment_date"
              type="date"
              class="input-field"
              required
            />
          </div>

          <div v-if="paymentForm.status === 'paid'">
            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
            <select v-model="paymentForm.payment_method" class="input-field">
              <option value="bank_transfer">Bank Transfer</option>
              <option value="credit_card">Credit Card</option>
              <option value="cash">Cash</option>
              <option value="cheque">Cheque</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div v-if="paymentForm.status === 'paid'">
            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Reference</label>
            <input
              v-model="paymentForm.payment_reference"
              type="text"
              class="input-field"
              placeholder="Transaction ID, cheque number, etc."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
            <textarea
              v-model="paymentForm.notes"
              class="input-field"
              rows="2"
              placeholder="Additional notes about this payment..."
            ></textarea>
          </div>

          <div class="flex justify-end space-x-4 pt-4">
            <button type="button" @click="showPaymentModal = false" class="btn-secondary">Cancel</button>
            <button type="submit" class="btn-primary">Update Status</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

const newInvoice = ref({
  client: '',
  weekStart: '',
  weekEnd: ''
})

const showPreview = ref(false)

const invoices = ref([])
const summary = ref({})
const loading = ref(false)

// Load invoices from API
async function loadInvoices() {
  try {
    loading.value = true
    const response = await fetch('/api/invoices')
    const data = await response.json()
    invoices.value = data.invoices || []
    summary.value = data.summary || {}
  } catch (error) {
    console.error('Error loading invoices:', error)
  } finally {
    loading.value = false
  }
}

// Load invoices on mount and optionally open a specific invoice via query param
import { onMounted } from 'vue'
onMounted(async () => {
  await loadInvoices()
  const params = new URLSearchParams(window.location.search)
  const targetId = params.get('invoice')
  if (targetId) {
    const inv = invoices.value.find(i => String(i.id) === String(targetId))
    if (inv) viewInvoice(inv)
  }
})

// Payment tracking
const showPaymentModal = ref(false)
const selectedInvoice = ref(null)
const paymentForm = ref({
  status: 'paid',
  payment_date: new Date().toISOString().split('T')[0],
  payment_method: 'bank_transfer',
  payment_reference: '',
  notes: ''
})

function updatePayment(invoice) {
  selectedInvoice.value = invoice
  paymentForm.value = {
    status: invoice.status === 'pending' ? 'paid' : invoice.status,
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'bank_transfer',
    payment_reference: '',
    notes: ''
  }
  showPaymentModal.value = true
}

async function savePaymentUpdate() {
  try {
    const response = await fetch(`/api/invoices/${selectedInvoice.value.id}/payment`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(paymentForm.value)
    })

    if (response.ok) {
      showPaymentModal.value = false
      loadInvoices() // Reload to show updated status
    } else {
      console.error('Error updating payment status')
    }
  } catch (error) {
    console.error('Error updating payment:', error)
  }
}

function viewInvoice(invoice) {
  // Open the HTML file if it exists
  if (invoice.file_path) {
    window.open(`/${invoice.file_path}`, '_blank')
  } else {
    showPreview.value = true
  }
}

async function downloadInvoice(invoice) {
  try {
    // Download the HTML file from the API
    const response = await fetch(`/api/invoices/${invoice.invoice_number}/download`)

    if (!response.ok) {
      if (response.status === 404) {
        alert('Invoice file not found. Try regenerating the invoice.')
      } else {
        alert('Error downloading invoice')
      }
      return
    }

    // Create blob and download
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${invoice.invoice_number}_Complete.html`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error downloading invoice:', error)
    alert('Error downloading invoice')
  }
}

async function generateInvoice() {
  if (!newInvoice.value.client || !newInvoice.value.weekStart || !newInvoice.value.weekEnd) {
    alert('Please fill in all fields')
    return
  }

  try {
    // Map client name to client_id (FSU = 2)
    const clientId = newInvoice.value.client === 'FSU' ? 2 : 1

    const response = await fetch('/api/invoices/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_id: clientId,
        week_start: newInvoice.value.weekStart,
        week_end: newInvoice.value.weekEnd
      })
    })

    if (response.ok) {
      const result = await response.json()

      // Reset form
      newInvoice.value = {
        client: '',
        weekStart: '',
        weekEnd: ''
      }

      // Reload invoices
      loadInvoices()

      alert(`Invoice ${result.invoice_number} generated successfully!\nTotal Hours: ${result.total_hours}\nTotal Amount: $${result.total_amount}`)
    } else {
      const errorData = await response.json()
      console.error('Error generating invoice:', errorData)
      alert(`Error generating invoice: ${errorData.error || 'Unknown error'}`)
    }
  } catch (error) {
    console.error('Error generating invoice:', error)
    alert('Error generating invoice')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatPeriod = (start: string, end: string) => {
  const startDate = new Date(start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  const endDate = new Date(end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  return `${startDate} - ${endDate}`
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'overdue':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>
