# Legacy Invoice Generators Archive

This directory contains the old CSV-based invoice generation system that has been retired in favor of the modern database-based system.

## 📁 Archived Files

### `csv-based-invoice-generator.py`
- **Original location**: `scripts/invoice_generator.py`
- **Purpose**: CSV-based invoice generator (retired)
- **Reason for retirement**: Replaced by database-based system in `python-service/invoice_generator.py`
- **Last used**: September 2025

### `working-files-automation/`
- **Original location**: `working-files/automation/`
- **Purpose**: Development/testing versions of automation scripts
- **Reason for retirement**: Consolidated into main python-service
- **Contains**: 
  - `invoice_generator.py` (CSV-based)
  - Various HTML templates and test files

### `verify_invoices.py`
- **Original location**: `python-service/verify_invoices.py`
- **Purpose**: Verification script for old invoice system
- **Reason for retirement**: No longer needed with database-based system

## 🔄 Migration to Database System

**Old System (CSV-based)**:
- Required manual CSV file management
- Hardcoded payment terms
- No client-specific settings
- Manual invoice numbering

**New System (Database-based)**:
- ✅ Automatic data from database
- ✅ Client-specific payment terms
- ✅ Project-specific hourly rates
- ✅ API integration
- ✅ Automatic invoice numbering
- ✅ Better error handling

## 📋 Current Invoice Generation

**Active System**: `python-service/invoice_generator.py`
**Triggered by**: API endpoint `/api/invoices/generate`
**Data source**: SQLite database (`data/calmren.db`)
**Output**: Professional HTML invoices with correct rates and payment terms

## 🗃️ Historical Reference

These files are kept for historical reference and in case any legacy functionality needs to be reviewed. They should not be used for new invoice generation.

**Date Archived**: September 29, 2025
**Archived by**: System cleanup and modernization
