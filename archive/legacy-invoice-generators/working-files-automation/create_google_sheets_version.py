#!/usr/bin/env python3
"""
Create Google Sheets-optimized version of Calmren Master Spreadsheet
Converts Excel formulas to Google Sheets format and adds native features
"""

import pandas as pd
import json
from datetime import datetime

def create_google_sheets_formulas():
    """Create Google Sheets-specific formulas and structure"""
    
    formulas = {
        'Dashboard': {
            'A1': 'CALMREN BUSINESS DASHBOARD',
            'A2': f'Last Updated: {datetime.now().strftime("%Y-%m-%d %H:%M")}',
            'A4': 'Metric',
            'B4': 'Current',
            'C4': 'Target',
            'A5': 'This Month Hours',
            'B5': '=SUMIFS(TimeLog!G:G,TimeLog!B:B,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
            'C5': '160',
            'A6': 'This Month Revenue',
            'B6': '=SUMIFS(TimeLog!I:I,TimeLog!B:B,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
            'C6': '12800',
            'A7': 'Active Clients',
            'B7': '=COUNTA(UNIQUE(FILTER(TimeLog!C:C,TimeLog!B:B>=DATE(YEAR(TODAY()),MONTH(TODAY()),1))))',
            'C7': '5',
            'A8': 'Average Rate',
            'B8': '=AVERAGE(FILTER(TimeLog!H:H,TimeLog!H:H>0))',
            'C8': '80',
            'A9': 'Pending Invoices',
            'B9': '=COUNTIF(InvoicingDashboard!H:H,"Pending")',
            'C9': '0',
            'A10': 'Outstanding Amount',
            'B10': '=SUMIF(InvoicingDashboard!H:H,"Pending",InvoicingDashboard!E:E)',
            'C10': '0'
        },
        
        'TimeLog': {
            # Headers
            'A1': 'Timestamp',
            'B1': 'Date', 
            'C1': 'Client',
            'D1': 'Project',
            'E1': 'Start Time',
            'F1': 'End Time',
            'G1': 'Duration (Hours)',
            'H1': 'Hourly Rate',
            'I1': 'Amount',
            'J1': 'Task Description',
            'K1': 'Status',
            
            # Sample formulas for row 2 (to be copied down)
            'G2': '=IF(AND(E2<>"",F2<>""),IF(F2>E2,(F2-E2)*24,(F2+1-E2)*24),"")',
            'H2': '80',  # Default rate
            'I2': '=IF(AND(G2<>"",H2<>""),G2*H2,"")',
            'K2': '=IF(I2<>"","Logged","")'
        },
        
        'InvoicingDashboard': {
            'A1': 'Invoice #',
            'B1': 'Client',
            'C1': 'Project', 
            'D1': 'Total Hours',
            'E1': 'Amount to Bill',
            'F1': 'Hourly Rate',
            'G1': 'Invoice Date',
            'H1': 'Payment Status',
            'I1': 'Due Date',
            
            # Sample formulas
            'A2': '=IF(B2<>"","INV-"&TEXT(ROW()-1,"000"),"")',
            'D2': '=SUMIFS(TimeLog!G:G,TimeLog!C:C,B2,TimeLog!D:D,C2)',
            'E2': '=D2*F2',
            'F2': '80',
            'H2': '=IF(G2="","Not Invoiced",IF(TODAY()>I2,"OVERDUE","Pending"))',
            'I2': '=IF(G2<>"",G2+30,"")'
        },
        
        'MonthlyTotals': {
            'A1': 'Month',
            'B1': 'Client',
            'C1': 'Project',
            'D1': 'Hours',
            'E1': 'Revenue',
            'F1': 'Avg Rate',
            
            # This will be populated by QUERY formula
            'A3': '=QUERY(TimeLog!B:I,"SELECT MONTH(B), C, D, SUM(G), SUM(I), AVG(H) WHERE B IS NOT NULL GROUP BY MONTH(B), C, D ORDER BY MONTH(B) DESC",1)'
        },
        
        'Expenses': {
            'A1': 'Date',
            'B1': 'Category',
            'C1': 'Supplier',
            'D1': 'Description',
            'E1': 'Amount (excl GST)',
            'F1': 'GST Included?',
            'G1': 'GST Amount',
            'H1': 'Total Amount',
            'I1': 'Receipt Link',
            'J1': 'Payment Method',
            
            # Sample formulas
            'G2': '=IF(F2="Yes",E2*0.15,0)',
            'H2': '=E2+G2'
        }
    }
    
    return formulas

def create_apps_script_code():
    """Generate Google Apps Script code for automation"""
    
    script_code = '''
/**
 * Calmren Business Automation Scripts
 * Auto-calculates time entries and manages invoicing
 */

// Trigger when form is submitted
function onFormSubmit(e) {
  console.log('Form submitted, processing...');
  
  var sheet = SpreadsheetApp.getActiveSheet();
  var lastRow = sheet.getLastRow();
  
  // Calculate duration if start and end times are provided
  calculateDuration(sheet, lastRow);
  
  // Calculate amount
  calculateAmount(sheet, lastRow);
  
  // Update dashboard
  updateDashboard();
  
  // Send notification (optional)
  // sendTimeEntryNotification(sheet, lastRow);
}

function calculateDuration(sheet, row) {
  try {
    var startTime = sheet.getRange(row, 5).getValue(); // Column E
    var endTime = sheet.getRange(row, 6).getValue();   // Column F
    
    if (startTime && endTime) {
      var duration;
      if (endTime > startTime) {
        duration = (endTime - startTime) / (1000 * 60 * 60); // Hours
      } else {
        // Handle overnight work (end time next day)
        duration = ((endTime + 24*60*60*1000) - startTime) / (1000 * 60 * 60);
      }
      
      sheet.getRange(row, 7).setValue(duration); // Column G
      console.log('Duration calculated: ' + duration + ' hours');
    }
  } catch (error) {
    console.error('Error calculating duration: ' + error);
  }
}

function calculateAmount(sheet, row) {
  try {
    var duration = sheet.getRange(row, 7).getValue(); // Column G
    var hourlyRate = sheet.getRange(row, 8).getValue() || 80; // Column H, default $80
    
    if (duration && hourlyRate) {
      var amount = duration * hourlyRate;
      sheet.getRange(row, 9).setValue(amount); // Column I
      sheet.getRange(row, 11).setValue('Logged'); // Column K - Status
      console.log('Amount calculated: $' + amount);
    }
  } catch (error) {
    console.error('Error calculating amount: ' + error);
  }
}

function updateDashboard() {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var dashboard = ss.getSheetByName('Dashboard');
    
    // Update last updated timestamp
    dashboard.getRange('B2').setValue('Last Updated: ' + new Date().toLocaleString());
    
    console.log('Dashboard updated');
  } catch (error) {
    console.error('Error updating dashboard: ' + error);
  }
}

// Generate invoice for specific client/project
function generateInvoice(clientName, projectName) {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var timeSheet = ss.getSheetByName('TimeLog');
    var invoiceSheet = ss.getSheetByName('InvoicingDashboard');
    
    // Get all time entries for this client/project
    var timeData = timeSheet.getDataRange().getValues();
    var totalHours = 0;
    var totalAmount = 0;
    var hourlyRate = 80; // Default
    
    for (var i = 1; i < timeData.length; i++) {
      if (timeData[i][2] === clientName && timeData[i][3] === projectName) {
        totalHours += timeData[i][6] || 0; // Duration
        totalAmount += timeData[i][8] || 0; // Amount
        if (timeData[i][7]) hourlyRate = timeData[i][7]; // Get actual rate
      }
    }
    
    if (totalHours > 0) {
      // Add to invoice sheet
      var lastRow = invoiceSheet.getLastRow() + 1;
      var invoiceNumber = 'INV-' + String(lastRow - 1).padStart(3, '0');
      
      invoiceSheet.getRange(lastRow, 1, 1, 9).setValues([[
        invoiceNumber,
        clientName,
        projectName,
        totalHours,
        totalAmount,
        hourlyRate,
        new Date(),
        'Pending',
        new Date(Date.now() + 30*24*60*60*1000) // Due in 30 days
      ]]);
      
      console.log('Invoice generated: ' + invoiceNumber);
      return invoiceNumber;
    }
  } catch (error) {
    console.error('Error generating invoice: ' + error);
  }
}

// Send email notification for new time entry
function sendTimeEntryNotification(sheet, row) {
  try {
    var data = sheet.getRange(row, 1, 1, 11).getValues()[0];
    var client = data[2];
    var project = data[3];
    var duration = data[6];
    var amount = data[8];
    
    var subject = 'New Time Entry Logged - ' + client;
    var body = `
New time entry recorded:

Client: ${client}
Project: ${project}
Duration: ${duration} hours
Amount: $${amount}
Date: ${new Date().toDateString()}

View your dashboard: ${SpreadsheetApp.getActiveSpreadsheet().getUrl()}
    `;
    
    // Replace with your email
    GmailApp.sendEmail('<EMAIL>', subject, body);
    
  } catch (error) {
    console.error('Error sending notification: ' + error);
  }
}

// Monthly summary report
function generateMonthlySummary() {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var timeSheet = ss.getSheetByName('TimeLog');
    
    var today = new Date();
    var firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    var lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    var data = timeSheet.getDataRange().getValues();
    var monthlyHours = 0;
    var monthlyRevenue = 0;
    var clientSummary = {};
    
    for (var i = 1; i < data.length; i++) {
      var entryDate = new Date(data[i][1]);
      if (entryDate >= firstDay && entryDate <= lastDay) {
        monthlyHours += data[i][6] || 0;
        monthlyRevenue += data[i][8] || 0;
        
        var client = data[i][2];
        if (!clientSummary[client]) {
          clientSummary[client] = {hours: 0, revenue: 0};
        }
        clientSummary[client].hours += data[i][6] || 0;
        clientSummary[client].revenue += data[i][8] || 0;
      }
    }
    
    // Create summary email
    var subject = 'Monthly Summary - ' + (today.getMonth() + 1) + '/' + today.getFullYear();
    var body = `Monthly Business Summary:

Total Hours: ${monthlyHours.toFixed(2)}
Total Revenue: $${monthlyRevenue.toFixed(2)}
Average Rate: $${(monthlyRevenue/monthlyHours).toFixed(2)}/hour

Client Breakdown:
`;
    
    for (var client in clientSummary) {
      body += `${client}: ${clientSummary[client].hours.toFixed(2)} hours, $${clientSummary[client].revenue.toFixed(2)}\\n`;
    }
    
    // Send email
    GmailApp.sendEmail('<EMAIL>', subject, body);
    
    console.log('Monthly summary sent');
    
  } catch (error) {
    console.error('Error generating monthly summary: ' + error);
  }
}

// Set up time-driven triggers
function createTriggers() {
  // Delete existing triggers
  var triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(function(trigger) {
    ScriptApp.deleteTrigger(trigger);
  });
  
  // Create new triggers
  ScriptApp.newTrigger('generateMonthlySummary')
    .timeBased()
    .onMonthDay(1)
    .atHour(9)
    .create();
    
  console.log('Triggers created successfully');
}
'''
    
    return script_code

def create_google_sheets_structure():
    """Create the complete Google Sheets structure"""
    
    print("🚀 Creating Google Sheets-optimized structure...")
    
    # Get formulas
    formulas = create_google_sheets_formulas()
    
    # Create structure document
    structure = {
        'spreadsheet_name': 'Calmren Master - Google Sheets',
        'sheets': {
            'Dashboard': {
                'purpose': 'Executive summary with live KPIs',
                'formulas': formulas['Dashboard'],
                'features': [
                    'Real-time metrics from all sheets',
                    'Visual progress indicators', 
                    'Monthly targets tracking',
                    'Last updated timestamp'
                ]
            },
            'TimeLog': {
                'purpose': 'Time tracking with Google Form integration',
                'formulas': formulas['TimeLog'],
                'features': [
                    'Direct Google Form input',
                    'Automatic duration calculation',
                    'Amount calculation based on hourly rate',
                    'Status tracking'
                ]
            },
            'InvoicingDashboard': {
                'purpose': 'Invoice generation and tracking',
                'formulas': formulas['InvoicingDashboard'],
                'features': [
                    'Auto invoice numbering',
                    'Payment status tracking',
                    'Due date calculation',
                    'Overdue highlighting'
                ]
            },
            'MonthlyTotals': {
                'purpose': 'Automated monthly summaries',
                'formulas': formulas['MonthlyTotals'],
                'features': [
                    'QUERY-based auto-population',
                    'Client and project breakdown',
                    'Monthly trend analysis'
                ]
            },
            'Expenses': {
                'purpose': 'Expense tracking with GST automation',
                'formulas': formulas['Expenses'],
                'features': [
                    'Automatic GST calculation',
                    'Category validation',
                    'Receipt link storage'
                ]
            }
        },
        'automation': {
            'apps_script': 'Handles form submissions and calculations',
            'triggers': [
                'onFormSubmit - Process new time entries',
                'Monthly summary - Generate reports',
                'Dashboard updates - Real-time metrics'
            ]
        }
    }
    
    return structure

def main():
    """Create Google Sheets version"""
    
    print("🎯 Creating Google Sheets-Optimized Calmren Master...")
    
    # Create structure
    structure = create_google_sheets_structure()
    
    # Save structure as JSON for reference
    with open('google_sheets_structure.json', 'w') as f:
        json.dump(structure, f, indent=2)
    
    # Create Apps Script code
    script_code = create_apps_script_code()
    
    with open('calmren_apps_script.js', 'w') as f:
        f.write(script_code)
    
    print("✅ Google Sheets structure created!")
    print("\n📁 Files created:")
    print("   - google_sheets_structure.json (Complete structure)")
    print("   - calmren_apps_script.js (Automation code)")
    
    print("\n🚀 Next Steps:")
    print("1. Create new Google Sheet with the structure")
    print("2. Add the Apps Script code for automation")
    print("3. Set up Google Form integration")
    print("4. Test the automated workflows")
    
    return True

if __name__ == "__main__":
    main()
