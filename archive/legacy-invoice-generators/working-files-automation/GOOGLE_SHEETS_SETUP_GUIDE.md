# 🚀 Google Sheets Setup Guide - <PERSON><PERSON>ren Master

## 🎯 What You're Getting

A fully automated Google Sheets system that:
✅ **Auto-calculates** time entries from your Google Form  
✅ **Generates invoices** with one click  
✅ **Tracks payments** and highlights overdue items  
✅ **Updates dashboard** in real-time  
✅ **Sends email notifications** for important events  
✅ **Works perfectly on mobile** for on-the-go tracking  

## 📋 Step-by-Step Setup

### Step 1: Create Your Google Sheet
1. Go to [sheets.google.com](https://sheets.google.com)
2. Create a new blank spreadsheet
3. Name it "Calmren Master - Business Tracker"
4. Create these sheet tabs (rename Sheet1, add others):
   - **Dashboard**
   - **TimeLog** 
   - **InvoicingDashboard**
   - **MonthlyTotals**
   - **Expenses**
   - **VehicleLog**

### Step 2: Set Up the Dashboard Sheet
Copy these formulas into your Dashboard sheet:

```
A1: CALMREN BUSINESS DASHBOARD
A2: Last Updated: [Will auto-update]

A4: Metric               B4: Current    C4: Target
A5: This Month Hours     B5: =SUMIFS(TimeLog!G:G,TimeLog!B:B,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))    C5: 160
A6: This Month Revenue   B6: =SUMIFS(TimeLog!I:I,TimeLog!B:B,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),TimeLog!B:B,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))    C6: 12800
A7: Active Clients       B7: =COUNTA(UNIQUE(FILTER(TimeLog!C:C,TimeLog!B:B>=DATE(YEAR(TODAY()),MONTH(TODAY()),1))))    C7: 5
A8: Average Rate         B8: =AVERAGE(FILTER(TimeLog!H:H,TimeLog!H:H>0))    C8: 80
A9: Pending Invoices     B9: =COUNTIF(InvoicingDashboard!H:H,"Pending")    C9: 0
A10: Outstanding Amount  B10: =SUMIF(InvoicingDashboard!H:H,"Pending",InvoicingDashboard!E:E)    C10: 0
```

### Step 3: Set Up TimeLog Sheet Headers
```
A1: Timestamp    B1: Date    C1: Client    D1: Project    E1: Start Time    
F1: End Time     G1: Duration (Hours)    H1: Hourly Rate    I1: Amount    
J1: Task Description    K1: Status
```

**Add these formulas in row 2 (copy down as needed):**
```
G2: =IF(AND(E2<>"",F2<>""),IF(F2>E2,(F2-E2)*24,(F2+1-E2)*24),"")
H2: 80
I2: =IF(AND(G2<>"",H2<>""),G2*H2,"")
K2: =IF(I2<>"","Logged","")
```

### Step 4: Set Up InvoicingDashboard Headers
```
A1: Invoice #    B1: Client    C1: Project    D1: Total Hours    E1: Amount to Bill    
F1: Hourly Rate    G1: Invoice Date    H1: Payment Status    I1: Due Date
```

**Add these formulas in row 2:**
```
A2: =IF(B2<>"","INV-"&TEXT(ROW()-1,"000"),"")
D2: =SUMIFS(TimeLog!G:G,TimeLog!C:C,B2,TimeLog!D:D,C2)
E2: =D2*F2
F2: 80
H2: =IF(G2="","Not Invoiced",IF(TODAY()>I2,"OVERDUE","Pending"))
I2: =IF(G2<>"",G2+30,"")
```

### Step 5: Add Google Apps Script Automation
1. In your Google Sheet, go to **Extensions → Apps Script**
2. Delete the default code
3. Copy and paste the code from `calmren_apps_script.js`
4. Save the project (name it "Calmren Automation")

### Step 6: Set Up Form Integration
1. Create a new Google Form
2. Add these fields:
   - **Date** (Date picker, required)
   - **Client** (Dropdown: FSU, Client2, Client3, Other)
   - **Project** (Dropdown: MFA Setup, Admin Overview, CEO Questions, Other)
   - **Start Time** (Time picker, required)
   - **End Time** (Time picker, required)  
   - **Task Description** (Long text, required)
   - **Hourly Rate** (Number, default: 80)

3. Link form to your TimeLog sheet:
   - In form, click **Responses** tab
   - Click Google Sheets icon
   - Select "Select existing spreadsheet"
   - Choose your Calmren Master sheet
   - Select TimeLog sheet

### Step 7: Set Up Automation Triggers
1. In Apps Script, click **Triggers** (clock icon)
2. Click **+ Add Trigger**
3. Set up these triggers:

**Form Submission Trigger:**
- Function: `onFormSubmit`
- Event source: From spreadsheet
- Event type: On form submit

**Monthly Report Trigger:**
- Function: `generateMonthlySummary`
- Event source: Time-driven
- Type: Month timer
- Day of month: 1
- Hour: 9am

### Step 8: Test Your Setup
1. **Submit a test form entry**
2. **Check TimeLog sheet** - should auto-calculate duration and amount
3. **Check Dashboard** - should show updated metrics
4. **Test invoice generation** - add client/project data to InvoicingDashboard

## 📱 Mobile Optimization

### Quick Access Setup
1. **Bookmark the form** on your phone's home screen
2. **Install Google Sheets app** for dashboard access
3. **Enable offline access** in Sheets app settings

### Form Shortcuts
- **Voice input** for task descriptions
- **Quick client selection** from dropdown
- **Time picker** for easy start/end time entry

## 🔧 Customization Options

### Add Your Clients
Edit the dropdown options in your Google Form:
1. Open form editor
2. Click on Client field
3. Add your actual client names
4. Do the same for Project field

### Adjust Hourly Rates
- **Default rate**: Change the "80" in TimeLog H2
- **Client-specific rates**: Add rates in InvoicingDashboard F column
- **Project-specific rates**: Create lookup table if needed

### Email Notifications
Update the email address in Apps Script:
1. Find `<EMAIL>` in the code
2. Replace with your actual email
3. Save the script

## 🎨 Visual Enhancements

### Conditional Formatting
1. **Overdue Invoices**: Select InvoicingDashboard H:H, Format → Conditional formatting
   - Format cells if: Text is exactly "OVERDUE"
   - Background color: Light red

2. **Dashboard Targets**: Select Dashboard B5:B10
   - Format cells if: Greater than or equal to C5:C10
   - Background color: Light green

### Charts and Graphs
Add visual charts to your Dashboard:
1. **Monthly Revenue Trend**: Line chart from MonthlyTotals
2. **Client Distribution**: Pie chart of hours by client
3. **Project Profitability**: Bar chart of revenue by project

## 🔒 Security & Backup

### Sharing Settings
1. **Keep private**: Don't share unless necessary
2. **Form access**: Share form link only with authorized users
3. **View-only access**: For clients who need to see their data

### Automatic Backup
Google Sheets auto-saves, but also:
1. **Weekly export**: Download as Excel backup
2. **Version history**: File → Version history → See version history
3. **Google Drive backup**: Sheets automatically backed up to Drive

## 🚀 Advanced Features

### Invoice PDF Generation
Add this function to Apps Script for PDF invoices:
```javascript
function createInvoicePDF(invoiceRow) {
  // Get invoice data
  var sheet = SpreadsheetApp.getActiveSheet();
  var data = sheet.getRange(invoiceRow, 1, 1, 9).getValues()[0];
  
  // Create PDF from template
  // (Requires additional setup with Google Docs template)
}
```

### Client Portal Access
Create filtered views for clients:
1. **Data → Filter views → Create new filter view**
2. **Filter by client name**
3. **Share filtered view** with client

### Expense Receipt Processing
Integrate with Google Drive for receipt storage:
1. **Create "Receipts" folder** in Google Drive
2. **Link receipts** in Expenses sheet
3. **Auto-categorize** using Apps Script

## 📊 Success Metrics

### Track These KPIs
- **Time entry speed**: Target <2 minutes per entry
- **Invoice generation**: Target <5 minutes per invoice
- **Dashboard accuracy**: Real-time updates
- **Mobile usage**: % of entries from mobile

### Monthly Review
1. **Check dashboard targets** vs. actual performance
2. **Review client profitability** from MonthlyTotals
3. **Analyze time patterns** for productivity insights
4. **Update rates and targets** as needed

## 🎯 Quick Start Checklist

### Week 1: Basic Setup
- [ ] Create Google Sheet with all tabs
- [ ] Add formulas to Dashboard and TimeLog
- [ ] Set up Google Form
- [ ] Link form to TimeLog sheet
- [ ] Test form submission

### Week 2: Automation
- [ ] Add Apps Script code
- [ ] Set up form submission trigger
- [ ] Test automatic calculations
- [ ] Customize client/project lists
- [ ] Set up email notifications

### Week 3: Enhancement
- [ ] Add conditional formatting
- [ ] Create charts and visualizations
- [ ] Set up monthly reporting
- [ ] Test invoice generation
- [ ] Optimize mobile workflow

### Week 4: Go Live
- [ ] Train on new workflow
- [ ] Document custom procedures
- [ ] Set up backup routine
- [ ] Plan future enhancements

## 💡 Pro Tips

1. **Start simple**: Get basic time tracking working first
2. **Test thoroughly**: Submit several test entries before going live
3. **Mobile first**: Design workflow for phone use
4. **Regular backups**: Export monthly Excel backups
5. **Monitor performance**: Check Apps Script execution logs

## 🆘 Troubleshooting

### Common Issues
- **Formulas not calculating**: Check cell references and sheet names
- **Form not linking**: Verify form destination settings
- **Apps Script errors**: Check execution logs in Apps Script editor
- **Mobile issues**: Use Google Sheets app instead of browser

### Getting Help
- **Google Sheets Help**: Built-in help system
- **Apps Script Documentation**: script.google.com
- **Community Forums**: Reddit r/googlesheets
- **Video Tutorials**: YouTube Google Sheets channels

---

## 🎉 You're Ready to Go!

Your Google Sheets system will save you hours every week and give you real-time insights into your business. The automation handles the tedious calculations while you focus on client work.

**Questions?** The setup might seem complex, but each step builds on the previous one. Start with the basic sheet structure and add automation gradually!
