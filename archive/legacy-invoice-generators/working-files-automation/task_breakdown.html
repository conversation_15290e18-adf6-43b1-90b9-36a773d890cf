<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Breakdown - Week Ending 12 September 2025</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.5;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 15px;
        }
        
        .report-title {
            font-size: 2.2em;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .period {
            font-size: 1.1em;
            color: #666;
        }
        
        .summary-box {
            background-color: #e3f2fd;
            border-left: 5px solid #2c5aa0;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .day-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .day-header {
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .day-summary {
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            font-size: 0.95em;
        }
        
        .task-list {
            background-color: white;
        }
        
        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-time {
            font-weight: bold;
            color: #2c5aa0;
            min-width: 60px;
            font-family: 'Courier New', monospace;
        }
        
        .task-project {
            background-color: #e8f4f8;
            color: #2c5aa0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }
        
        .it-continuity {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .task-description {
            flex: 1;
            padding-left: 10px;
        }
        
        .project-summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        
        .project-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .project-card.it-admin {
            border-color: #2c5aa0;
        }
        
        .project-card.it-continuity {
            border-color: #856404;
        }
        
        .project-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c5aa0;
        }
        
        .project-card.it-continuity .project-title {
            color: #856404;
        }
        
        .project-hours {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .key-activities {
            margin-top: 15px;
        }
        
        .key-activities ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .key-activities li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .footer-note {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="report-title">DETAILED TASK BREAKDOWN</h1>
        <div class="period">Week Ending 12 September 2025 • FSU Client Services</div>
    </div>

    <div class="summary-box">
        <h2 style="margin-top: 0; color: #2c5aa0;">Weekly Summary</h2>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-value">19.5</div>
                <div class="stat-label">Total Hours</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">9</div>
                <div class="stat-label">Task Entries</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">2</div>
                <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">$1,560</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>
    </div>

    <div class="project-summary">
        <div class="project-card it-admin">
            <div class="project-title">IT Administration</div>
            <div class="project-hours" style="color: #2c5aa0;">5.5 hours</div>
            <div class="key-activities">
                <strong>Key Activities:</strong>
                <ul>
                    <li>Email content verification and validation</li>
                    <li>Research compilation and template setup</li>
                    <li>NotebookLM configuration for team sharing</li>
                    <li>Offboarding documentation creation</li>
                    <li>M365 tenant management and licence changes</li>
                    <li>Offboarding checklists development</li>
                </ul>
            </div>
        </div>
        
        <div class="project-card it-continuity">
            <div class="project-title">IT Continuity</div>
            <div class="project-hours" style="color: #856404;">14.0 hours</div>
            <div class="key-activities">
                <strong>Key Activities:</strong>
                <ul>
                    <li>Digital ID project research and workflow planning</li>
                    <li>Nucleus and Craft CMS evaluation</li>
                    <li>Security reviews and access management</li>
                    <li>Social media access auditing</li>
                    <li>Staff offboarding procedures</li>
                    <li>Facebook security and access management</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Daily Breakdown -->
    <div class="day-section">
        <div class="day-header">Saturday, 7 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 0.5 hours • <strong>Focus:</strong> Email verification</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">0.5h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Check and verify email content</div>
            </div>
        </div>
    </div>

    <div class="day-section">
        <div class="day-header">Sunday, 8 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 6.5 hours • <strong>Focus:</strong> Digital ID project initiation</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">6.5h</div>
                <div class="task-project it-continuity">IT Continuity</div>
                <div class="task-description">Digital ID Meeting and initial research structure, workflow with Lucy, reviewed Nucleus, Craft cms, covered logins and workflow with Nathan</div>
            </div>
        </div>
    </div>

    <div class="day-section">
        <div class="day-header">Monday, 9 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 4.0 hours • <strong>Focus:</strong> Research & access management</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">1.5h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Compiled Research, into templates, set up, notebooklm to share and use</div>
            </div>
            <div class="task-item">
                <div class="task-time">2.5h</div>
                <div class="task-project it-continuity">IT Continuity</div>
                <div class="task-description">Checked Sign-ins, reviewed CEO requests for Cal access</div>
            </div>
        </div>
    </div>

    <div class="day-section">
        <div class="day-header">Tuesday, 10 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 2.5 hours • <strong>Focus:</strong> Offboarding preparation</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">2.5h</div>
                <div class="task-project it-continuity">IT Continuity</div>
                <div class="task-description">Review current content signin and prep for Off boarding</div>
            </div>
        </div>
    </div>

    <div class="day-section">
        <div class="day-header">Wednesday, 11 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 4.0 hours • <strong>Focus:</strong> Social media audit & offboarding setup</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">1.5h</div>
                <div class="task-project it-continuity">IT Continuity</div>
                <div class="task-description">Complete a Social Media access check – created doc, and advised on missing access</div>
            </div>
            <div class="task-item">
                <div class="task-time">2.5h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Set up Offboarding Doc, received information for how to handle various items from leaving staff. setup admin to ensure a smooth signout.</div>
            </div>
        </div>
    </div>

    <div class="day-section">
        <div class="day-header">Thursday, 12 September 2025</div>
        <div class="day-summary"><strong>Daily Total:</strong> 2.0 hours • <strong>Focus:</strong> Security & offboarding execution</div>
        <div class="task-list">
            <div class="task-item">
                <div class="task-time">1.0h</div>
                <div class="task-project it-continuity">IT Continuity</div>
                <div class="task-description">Checked and secured access and off boarding for FB</div>
            </div>
            <div class="task-item">
                <div class="task-time">1.0h</div>
                <div class="task-project">IT Admin</div>
                <div class="task-description">Activated OffBoarding in M365 Tenant, confirmed email and inbox forwarding, changed licence. Created OffBoarding checklists</div>
            </div>
        </div>
    </div>

    <div class="footer-note">
        <p><strong>Report prepared:</strong> 13 September 2025<br>
        <strong>Next billing period:</strong> Week ending 19 September 2025</p>
        <p><em>All times recorded via automated time tracking system</em></p>
    </div>
</body>
</html>