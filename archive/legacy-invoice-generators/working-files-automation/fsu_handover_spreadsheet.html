<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSU Content Planner Handover Checklist</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .controls {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .filter-btn:hover, .filter-btn.active {
            background: #007bff;
            color: white;
        }
        
        .progress-bar {
            flex: 1;
            min-width: 200px;
            background: #e9ecef;
            border-radius: 20px;
            padding: 3px;
        }
        
        .progress-fill {
            height: 20px;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 20px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .table-container {
            overflow-x: auto;
            max-height: 70vh;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        th, td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid #dee2e6;
        }
        
        .priority-critical { border-left: 4px solid #dc3545; }
        .priority-high { border-left: 4px solid #fd7e14; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        
        .category {
            font-weight: 600;
            color: #495057;
            background: #f8f9fa;
        }
        
        .status-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .completed-row {
            background-color: #f8f9fa;
            opacity: 0.7;
        }
        
        .completed-row td {
            text-decoration: line-through;
        }
        
        .notes-cell {
            max-width: 300px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .assignee-select {
            width: 100%;
            padding: 4px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .date-input {
            width: 120px;
            padding: 4px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .export-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .export-btn:hover {
            background: #218838;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            font-size: 12px;
        }
        
        .stat-item {
            padding: 4px 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FSU Content Planner Handover Checklist</h1>
            <p>Complete Transition Guide & Access Audit</p>
        </div>
        
        <div class="controls">
            <button class="filter-btn active" onclick="filterByCategory('all')">All Tasks</button>
            <button class="filter-btn" onclick="filterByCategory('critical')">Critical Access</button>
            <button class="filter-btn" onclick="filterByCategory('tools')">Content Tools</button>
            <button class="filter-btn" onclick="filterByCategory('strategy')">Strategic Knowledge</button>
            <button class="filter-btn" onclick="filterByCategory('crisis')">Crisis Procedures</button>
            <button class="filter-btn" onclick="filterByPriority('critical')">Critical Priority</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar">0% Complete</div>
            </div>
            
            <div class="stats">
                <div class="stat-item">Total: <span id="totalTasks">0</span></div>
                <div class="stat-item">Completed: <span id="completedTasks">0</span></div>
                <div class="stat-item">Remaining: <span id="remainingTasks">0</span></div>
            </div>
            
            <button class="export-btn" onclick="exportToCSV()">Export CSV</button>
        </div>
        
        <div class="table-container">
            <table id="checklistTable">
                <thead>
                    <tr>
                        <th style="width: 40px;">✓</th>
                        <th style="width: 180px;">Category</th>
                        <th style="width: 120px;">Priority</th>
                        <th>Task/Item</th>
                        <th style="width: 300px;">Details/Notes</th>
                        <th style="width: 120px;">Assigned To</th>
                        <th style="width: 120px;">Due Date</th>
                        <th style="width: 120px;">Completed</th>
                        <th style="width: 200px;">Additional Notes</th>
                    </tr>
                </thead>
                <tbody id="checklistBody">
                    <!-- Table content will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const checklistData = [
            // Critical Access & Logins
            {
                category: 'Critical Access',
                priority: 'Critical',
                task: 'Facebook Business Manager Access',
                details: 'Login details for main admin account, 2FA backup codes, Business Manager admin permissions, Facebook Page admin access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Priority: MUST COMPLETE'
            },
            {
                category: 'Critical Access',
                priority: 'Critical',
                task: 'Instagram Business Account Access',
                details: 'Login: freespeechnz FSUnz2023 (Instagram reporting login invalid), Connected Facebook account, Creator Studio access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Login currently invalid - needs immediate attention'
            },
            {
                category: 'Critical Access',
                priority: 'Critical',
                task: 'X (Twitter) Account Access',
                details: 'Account: @NZFreeSpeech, Password: Pak34udjwIJLL2sdHHn8, TweetDeck access, API keys, backup email',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Critical Access',
                priority: 'Critical',
                task: 'TikTok Business Account Access',
                details: 'Login: freespeechunionnz, Password: ManOnTheStreet2024?, TikTok Business Centre access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Critical Access',
                priority: 'Critical',
                task: 'YouTube Channel Access',
                details: 'Account: <EMAIL>, Password: nuznot-jawveb-pipHa4 (changed 5 months ago), YouTube Studio access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Google account ownership, Brand account permissions, Analytics access'
            },
            {
                category: 'Critical Access',
                priority: 'High',
                task: 'LinkedIn Company Page Access',
                details: 'Admin access to FSU LinkedIn page, Campaign Manager access, personal account permissions',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Critical Access',
                priority: 'High',
                task: 'Email Platform Access',
                details: 'Mailchimp/ConvertKit login, admin permissions, SMTP settings, subscriber list backup',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Critical Access',
                priority: 'Medium',
                task: 'Podcast Platform Access',
                details: 'Buzzsprout: <EMAIL> fieugwf29ib39972gw2r9g (unused since July 11 2025), RSS feed control',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Platform not actively used - low priority'
            },

            // Content Tools & Systems
            {
                category: 'Content Tools',
                priority: 'High',
                task: 'Canva Account Access',
                details: 'Team access, brand kit access, template folder organisation, shared asset library',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Content Tools',
                priority: 'High',
                task: 'Video Editing Tools Access',
                details: 'Descript account access, video asset library location, export settings, shared projects',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Content Tools',
                priority: 'High',
                task: 'Google Workspace Access',
                details: 'Shared drives access, template documents, collaborative permissions, calendar access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Content Tools',
                priority: 'Medium',
                task: 'Analytics Tools Access',
                details: 'Google Analytics, social media analytics platforms, third-party monitoring, reporting dashboards',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Content Tools',
                priority: 'Medium',
                task: 'Content Scheduling Platform',
                details: 'Buffer/Hootsuite/Later login, connected platform permissions, scheduled content access',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'If used'
            },
            {
                category: 'Content Tools',
                priority: 'Medium',
                task: 'Project Management Tools',
                details: 'Asana/Trello/Monday.com access, workflow templates, team collaboration spaces',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },

            // Content Planner System Knowledge
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Excel Workbook Operation Training',
                details: 'Weekly planning sheet, dropdown menus, content status workflow, date and platform planning',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Draft → Ready → Scheduled → Published workflow'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Content Pillars Dashboard Understanding',
                details: '30/25/25/20% split strategy, formula calculations, rebalancing process, status tracking',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Critical for maintaining content balance'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Platform Strategy Guidelines',
                details: 'Optimal posting times (NZ timezone), content type preferences, KPI targets, best practices',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Monthly Overview Process',
                details: 'Monthly content themes, seasonal considerations, campaign planning, review process',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Analytics Dashboard Usage',
                details: 'Platform metrics tracking, reporting schedule, performance benchmarking, strategy adjustment',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },

            // Templates & Brand Resources
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Content Templates Access',
                details: 'Email newsletter template, social media templates, video templates, written content templates',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Includes Canva templates, video sequences, script formats'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Brand Guidelines & Voice',
                details: 'Brand standards document, messaging framework, voice & tone guidelines, visual identity',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Authoritative but approachable tone'
            },

            // Strategic Knowledge Transfer
            {
                category: 'Strategic Knowledge',
                priority: 'Critical',
                task: 'Content Pillar Strategy Understanding',
                details: 'Legal & Rights Updates (30%), Community Stories (25%), Educational Content (25%), Fourth pillar (20%)',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Core content strategy framework'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Platform-Specific Strategies',
                details: 'Email timing, Facebook engagement, Instagram visuals, X breaking news, TikTok education, YouTube long-form',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'LinkedIn B2B, Podcast "In That Case" series'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Audience & Community Knowledge',
                details: 'Key stakeholder contacts, member database, VIP/media contacts, partner organisations',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Community management protocols, response procedures'
            },

            // Crisis & Contingency Procedures
            {
                category: 'Crisis Procedures',
                priority: 'Critical',
                task: 'Crisis Communication Plan',
                details: 'Urgent post approval authority, 24/7 contact protocols, pre-approved statements, media response',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Emergency response framework'
            },
            {
                category: 'Crisis Procedures',
                priority: 'High',
                task: 'Technical Backup Plans',
                details: 'Account recovery procedures, content backup locations, alternative posting methods, technical support',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Crisis Procedures',
                priority: 'High',
                task: 'Legal & Compliance Procedures',
                details: 'Content approval process, fact-checking procedures, copyright standards, privacy risk assessment',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },

            // Key Contacts & Relationships
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Internal Team Directory',
                details: 'Content creation team, legal team, management hierarchy, IT support contacts',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'External Partners Directory',
                details: 'Service provider contacts, media contacts, partner organisations, guest expert database',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Includes graphic designers, video production, developers'
            },

            // Ongoing Operational Procedures
            {
                category: 'Strategic Knowledge',
                priority: 'High',
                task: 'Daily Operations Training',
                details: 'Morning routine checklist, platform monitoring, breaking news assessment, comment moderation',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Overnight activity monitoring, rapid response protocols'
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Weekly Planning Process',
                details: 'Content calendar population, cross-platform adaptation, stakeholder approval, performance review',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: ''
            },
            {
                category: 'Strategic Knowledge',
                priority: 'Medium',
                task: 'Monthly Review Process',
                details: 'Performance assessment, analytics compilation, KPI review, content pillar evaluation',
                assignee: '',
                dueDate: '',
                completed: false,
                notes: 'Strategy adjustment recommendations'
            }
        ];

        function populateTable(data = checklistData) {
            const tbody = document.getElementById('checklistBody');
            tbody.innerHTML = '';
            
            data.forEach((item, index) => {
                const row = document.createElement('tr');
                row.className = item.completed ? 'completed-row' : '';
                row.classList.add(`priority-${item.priority.toLowerCase()}`);
                row.setAttribute('data-category', item.category.toLowerCase().replace(' ', '-'));
                row.setAttribute('data-priority', item.priority.toLowerCase());
                
                row.innerHTML = `
                    <td><input type="checkbox" class="status-checkbox" ${item.completed ? 'checked' : ''} onchange="toggleComplete(${index})"></td>
                    <td class="category">${item.category}</td>
                    <td><span class="priority-${item.priority.toLowerCase()}">${item.priority}</span></td>
                    <td><strong>${item.task}</strong></td>
                    <td class="notes-cell">${item.details}</td>
                    <td>
                        <select class="assignee-select" onchange="updateAssignee(${index}, this.value)">
                            <option value="">Select...</option>
                            <option value="Content Manager" ${item.assignee === 'Content Manager' ? 'selected' : ''}>Content Manager</option>
                            <option value="Social Media Lead" ${item.assignee === 'Social Media Lead' ? 'selected' : ''}>Social Media Lead</option>
                            <option value="Marketing Director" ${item.assignee === 'Marketing Director' ? 'selected' : ''}>Marketing Director</option>
                            <option value="Technical Lead" ${item.assignee === 'Technical Lead' ? 'selected' : ''}>Technical Lead</option>
                            <option value="Legal Team" ${item.assignee === 'Legal Team' ? 'selected' : ''}>Legal Team</option>
                            <option value="External Consultant" ${item.assignee === 'External Consultant' ? 'selected' : ''}>External Consultant</option>
                        </select>
                    </td>
                    <td><input type="date" class="date-input" value="${item.dueDate}" onchange="updateDueDate(${index}, this.value)"></td>
                    <td><input type="date" class="date-input" value="${item.completed ? new Date().toISOString().split('T')[0] : ''}" onchange="updateCompletedDate(${index}, this.value)"></td>
                    <td><input type="text" style="width: 100%; font-size: 12px; padding: 4px;" value="${item.notes}" onchange="updateNotes(${index}, this.value)" placeholder="Add notes..."></td>
                `;
                
                tbody.appendChild(row);
            });
            
            updateStats();
        }

        function toggleComplete(index) {
            checklistData[index].completed = !checklistData[index].completed;
            populateTable(getCurrentFilteredData());
        }

        function updateAssignee(index, value) {
            checklistData[index].assignee = value;
        }

        function updateDueDate(index, value) {
            checklistData[index].dueDate = value;
        }

        function updateCompletedDate(index, value) {
            // This could store completion date in a separate field
        }

        function updateNotes(index, value) {
            checklistData[index].notes = value;
        }

        function filterByCategory(category) {
            const buttons = document.querySelectorAll('.filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            let filteredData = checklistData;
            if (category !== 'all') {
                filteredData = checklistData.filter(item => 
                    item.category.toLowerCase().includes(category.toLowerCase())
                );
            }
            populateTable(filteredData);
        }

        function filterByPriority(priority) {
            const buttons = document.querySelectorAll('.filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const filteredData = checklistData.filter(item => 
                item.priority.toLowerCase() === priority.toLowerCase()
            );
            populateTable(filteredData);
        }

        function getCurrentFilteredData() {
            // This would return currently filtered data based on active filters
            return checklistData;
        }

        function updateStats() {
            const total = checklistData.length;
            const completed = checklistData.filter(item => item.completed).length;
            const remaining = total - completed;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
            
            document.getElementById('totalTasks').textContent = total;
            document.getElementById('completedTasks').textContent = completed;
            document.getElementById('remainingTasks').textContent = remaining;
            
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '% Complete';
        }

        function exportToCSV() {
            const headers = ['Status', 'Category', 'Priority', 'Task', 'Details', 'Assigned To', 'Due Date', 'Completed', 'Notes'];
            const csvContent = [
                headers.join(','),
                ...checklistData.map(item => [
                    item.completed ? 'Complete' : 'Pending',
                    item.category,
                    item.priority,
                    `"${item.task}"`,
                    `"${item.details}"`,
                    item.assignee,
                    item.dueDate,
                    item.completed ? 'Yes' : 'No',
                    `"${item.notes}"`
                ].join(','))
            ].join('\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'FSU_Content_Handover_Checklist.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // Initialize the table
        document.addEventListener('DOMContentLoaded', function() {
            populateTable();
        });
    </script>
</body>
</html>