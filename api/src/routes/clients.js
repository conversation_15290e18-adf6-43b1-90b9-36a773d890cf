import express from 'express'
import { getDatabase } from '../database/init.js'

const router = express.Router()

// Get all clients with invoice statistics
router.get('/', async (req, res) => {
  try {
    const db = getDatabase()

    const stmt = db.prepare(`SELECT
      c.*,
      COALESCE(pr.project_count, 0) AS project_count,
      COALESCE(te.time_entry_count, 0) AS time_entry_count,
      COALESCE(inv.invoice_count, 0) AS invoice_count,
      COALESCE(te.time_revenue, 0) AS time_revenue,
      COALESCE(inv.total_revenue, 0) AS total_revenue,
      COALESCE(inv.pending_amount, 0) AS pending_amount,
      inv.last_invoice_date AS last_invoice_date
     FROM clients c
     LEFT JOIN (
       SELECT client_id, COUNT(*) AS project_count
       FROM projects
       GROUP BY client_id
     ) pr ON pr.client_id = c.id
     LEFT JOIN (
       SELECT client_id,
              COUNT(*) AS time_entry_count,
              SUM(amount) AS time_revenue
       FROM time_entries
       GROUP BY client_id
     ) te ON te.client_id = c.id
     LEFT JOIN (
       SELECT client_id,
              COUNT(*) AS invoice_count,
              SUM(total_amount) AS total_revenue,
              SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) AS pending_amount,
              MAX(created_at) AS last_invoice_date
       FROM invoices
       GROUP BY client_id
     ) inv ON inv.client_id = c.id
     ORDER BY c.name`)

    const clients = stmt.all()

    res.json(clients)

  } catch (error) {
    console.error('Error getting clients:', error)
    res.status(500).json({ error: 'Failed to get clients' })
  }
})

// Get client summary statistics
router.get('/summary', async (req, res) => {
  try {
    const db = getDatabase()

    const stmt = db.prepare(`SELECT
      COUNT(DISTINCT c.id) as total_clients,
      COALESCE(SUM(i.total_amount), 0) as total_revenue,
      COALESCE(SUM(CASE WHEN i.status = 'pending' THEN i.total_amount ELSE 0 END), 0) as pending_amount,
      COALESCE(AVG(i.total_amount), 0) as avg_invoice
     FROM clients c
     LEFT JOIN invoices i ON c.id = i.client_id`)

    const summary = stmt.get()

    res.json(summary)

  } catch (error) {
    console.error('Error getting client summary:', error)
    res.status(500).json({ error: 'Failed to get client summary' })
  }
})

// Get single client with projects
router.get('/:id', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params

    const clientStmt = db.prepare('SELECT * FROM clients WHERE id = ?')
    const client = clientStmt.get(id)

    if (!client) {
      return res.status(404).json({ error: 'Client not found' })
    }

    const projectsStmt = db.prepare(`SELECT p.*,
     COUNT(te.id) as time_entry_count,
     COALESCE(SUM(te.duration), 0) as total_hours,
     COALESCE(SUM(te.amount), 0) as total_amount
     FROM projects p
     LEFT JOIN time_entries te ON p.id = te.project_id
     WHERE p.client_id = ?
     GROUP BY p.id
     ORDER BY p.name`)

    const projects = projectsStmt.all(id)

    res.json({ ...client, projects })

  } catch (error) {
    console.error('Error getting client:', error)
    res.status(500).json({ error: 'Failed to get client' })
  }
})

// Create new client
router.post('/', async (req, res) => {
  try {
    const db = getDatabase()
    const { name, email, phone, address, website, hourly_rate = 80, payment_terms = 7 } = req.body

    const stmt = db.prepare(`INSERT INTO clients (name, email, phone, address, website, hourly_rate, payment_terms)
     VALUES (?, ?, ?, ?, ?, ?, ?)`)

    const result = stmt.run(name, email, phone, address, website, hourly_rate, payment_terms)

    res.status(201).json({
      id: result.lastInsertRowid,
      message: 'Client created successfully'
    })

  } catch (error) {
    console.error('Error creating client:', error)
    res.status(500).json({ error: 'Failed to create client' })
  }
})

// Update client
router.put('/:id', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params
    const { name, email, phone, address, website, hourly_rate, payment_terms } = req.body

    const stmt = db.prepare(`UPDATE clients
     SET name = ?, email = ?, phone = ?, address = ?, website = ?, hourly_rate = ?, payment_terms = ?, updated_at = CURRENT_TIMESTAMP
     WHERE id = ?`)

    stmt.run(name, email, phone, address, website, hourly_rate, payment_terms, id)

    res.json({ message: 'Client updated successfully' })

  } catch (error) {
    console.error('Error updating client:', error)
    res.status(500).json({ error: 'Failed to update client' })
  }
})

// Get client projects
router.get('/:id/projects', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params

    const stmt = db.prepare('SELECT * FROM projects WHERE client_id = ? ORDER BY name')
    const projects = stmt.all(id)

    res.json(projects)

  } catch (error) {
    console.error('Error getting client projects:', error)
    res.status(500).json({ error: 'Failed to get client projects' })
  }
})

// Create new project for client
router.post('/:id/projects', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params
    const { name, description, status = 'active' } = req.body

    const stmt = db.prepare('INSERT INTO projects (client_id, name, description, status) VALUES (?, ?, ?, ?)')
    const result = stmt.run(id, name, description, status)

    res.status(201).json({
      id: result.lastInsertRowid,
      message: 'Project created successfully'
    })

  } catch (error) {
    console.error('Error creating project:', error)
    res.status(500).json({ error: 'Failed to create project' })
  }
})

export default router
