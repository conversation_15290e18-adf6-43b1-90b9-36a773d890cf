import express from 'express'
import { getDatabase } from '../database/init.js'
import { z } from 'zod'
import path from 'path'
import fs from 'fs'

// Validation schemas
const genSchema = z.object({
  client_id: z.coerce.number().int().positive(),
  week_start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  week_end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/)
})

const statusSchema = z.object({ status: z.enum(['paid','pending','overdue','cancelled','draft']) })

const paymentSchema = z.object({
  status: z.enum(['pending','paid','overdue','cancelled']),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  payment_method: z.string().optional(),
  payment_reference: z.string().optional(),
  notes: z.string().optional()
})


const router = express.Router()

// Get all invoices
router.get('/', async (req, res) => {
  try {
    const db = getDatabase()
    const { page = 1, limit = 20, status, client_id } = req.query

    let whereClause = '1=1'
    const params = []

    if (status) {
      whereClause += ' AND i.status = ?'
      params.push(status)
    }

    if (client_id) {
      whereClause += ' AND i.client_id = ?'
      params.push(client_id)
    }

    const offset = (page - 1) * limit
    params.push(limit, offset)

    const stmt = db.prepare(`SELECT
      i.*,
      c.name as client_name,
      c.email as client_email,
      CASE
        WHEN i.status = 'paid' THEN 'Paid'
        WHEN i.status = 'pending' THEN 'Pending'
        WHEN i.status = 'overdue' THEN 'Overdue'
        WHEN i.status = 'draft' THEN 'Draft'
        ELSE 'Unknown'
      END as status_display
     FROM invoices i
     LEFT JOIN clients c ON i.client_id = c.id
     WHERE ${whereClause}
     ORDER BY i.created_at DESC
     LIMIT ? OFFSET ?`)

    const invoices = stmt.all(...params)

    // Calculate summary statistics
    const summaryStmt = db.prepare(`SELECT
      COUNT(*) as total_count,
      SUM(total_amount) as total_revenue,
      SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
      SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) as pending_amount,
      SUM(CASE WHEN status = 'overdue' THEN total_amount ELSE 0 END) as overdue_amount
     FROM invoices i
     WHERE ${whereClause.replace(/LIMIT.*/, '')}`)

    const summary = summaryStmt.get(...params.slice(0, -2)) // Remove limit and offset params

    res.json({
      invoices,
      summary,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: summary.total_count
      }
    })

  } catch (error) {
    console.error('Error getting invoices:', error)
    res.status(500).json({ error: 'Failed to get invoices' })
  }
})

// Download invoice HTML file (must be before /:id route)
router.get('/:invoice_number/download', async (req, res) => {
  try {
    const { invoice_number } = req.params

    // Validate invoice exists in database
    const db = getDatabase()
    const invoice = db.prepare('SELECT * FROM invoices WHERE invoice_number = ?').get(invoice_number)

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' })
    }

    // Look for the HTML file
    const fileName = `${invoice_number}_Complete.html`
    const filePath = path.join('/app/invoices', fileName)

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Invoice file not found' })
    }

    // Set headers for download
    res.setHeader('Content-Type', 'text/html')
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`)

    // Send the file
    res.sendFile(filePath)

  } catch (error) {
    console.error('Error downloading invoice:', error)
    res.status(500).json({ error: 'Failed to download invoice' })
  }
})

// Get single invoice
router.get('/:id', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params

    const invStmt = db.prepare(`SELECT i.*, c.name as client_name, c.email as client_email
         FROM invoices i
         LEFT JOIN clients c ON i.client_id = c.id
         WHERE i.id = ?`)
    const invoice = invStmt.get(id)

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' })
    }

    res.json(invoice)

  } catch (error) {
    console.error('Error getting invoice:', error)
    res.status(500).json({ error: 'Failed to get invoice' })
  }
})

// Generate new invoice (trigger Python script)
router.post('/generate', async (req, res) => {
  try {
    const parsed = genSchema.safeParse(req.body)
    if (!parsed.success) return res.status(400).json({ error: 'Invalid payload', details: parsed.error.flatten() })
    const { client_id, week_start, week_end } = parsed.data

    // This would trigger your existing Python invoice generator
    // For now, we'll create a placeholder entry
    const db = getDatabase()

    // Calculate invoice number
    const year = new Date().getFullYear()
    const countRow = db.prepare('SELECT COUNT(*) as count FROM invoices WHERE invoice_number LIKE ?').get(`INV-${year}-%`)
    const count = countRow?.count || 0

    const invoiceNumber = `INV-${year}-${String(count + 1).padStart(3, '0')}`

    // Get time entries for the week
    const aggRow = db.prepare(`SELECT SUM(duration) as total_hours, SUM(amount) as total_amount
         FROM time_entries
         WHERE client_id = ? AND date BETWEEN ? AND ?`).get(client_id, week_start, week_end)
    const timeEntries = aggRow || { total_hours: 0, total_amount: 0 }

    const insertStmt = db.prepare(`INSERT INTO invoices
         (invoice_number, client_id, week_start, week_end, total_hours, total_amount, status)
         VALUES (?, ?, ?, ?, ?, ?, 'pending')`)
    const result = insertStmt.run(
      invoiceNumber,
      client_id,
      week_start,
      week_end,
      timeEntries.total_hours || 0,
      timeEntries.total_amount || 0
    )

    // Call Python service to generate HTML invoice file
    try {
      const pythonResponse = await fetch('http://python-service:8000/invoices/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ regenerate: true })
      })

      if (pythonResponse.ok) {
        const pythonResult = await pythonResponse.json()
        console.log('✅ Python service generated HTML files:', pythonResult.generated_files)
      } else {
        console.warn('⚠️ Python service failed to generate HTML files')
      }
    } catch (pythonError) {
      console.warn('⚠️ Could not reach Python service for HTML generation:', pythonError.message)
    }

    res.status(201).json({
      id: result.id,
      invoice_number: invoiceNumber,
      message: 'Invoice generated successfully',
      total_hours: timeEntries.total_hours || 0,
      total_amount: timeEntries.total_amount || 0
    })

  } catch (error) {
    console.error('Error generating invoice:', error)
    res.status(500).json({ error: 'Failed to generate invoice' })
  }
})

// Update invoice status
router.patch('/:id/status', async (req, res) => {
  try {
    const db = getDatabase()
    const { id } = req.params
    const parsed = statusSchema.safeParse(req.body)
    if (!parsed.success) return res.status(400).json({ error: 'Invalid payload', details: parsed.error.flatten() })
    const { status } = parsed.data

    const updateStmt = db.prepare('UPDATE invoices SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?')
    updateStmt.run(status, id)

    res.json({ message: 'Invoice status updated successfully' })

  } catch (error) {
    console.error('Error updating invoice status:', error)
    res.status(500).json({ error: 'Failed to update invoice status' })
  }
})

// Update invoice payment status
router.patch('/:id/payment', async (req, res) => {
  try {
    const { id } = req.params
    const parsed = paymentSchema.safeParse(req.body)
    if (!parsed.success) return res.status(400).json({ error: 'Invalid payload', details: parsed.error.flatten() })
    const { status, payment_date, payment_method, payment_reference, notes } = parsed.data

    const db = getDatabase()

    // Update invoice
    const payUpdateStmt = db.prepare(`UPDATE invoices
         SET status = ?, updated_at = datetime('now')
         WHERE id = ?`)
    const payUpdateRes = payUpdateStmt.run(status, id)
    if (payUpdateRes.changes === 0) throw new Error('Invoice not found')

    // If marking as paid, record payment details
    if (status === 'paid' && payment_date) {
      const payInsStmt = db.prepare(`INSERT OR REPLACE INTO invoice_payments
           (invoice_id, payment_date, payment_method, payment_reference, notes, created_at)
           VALUES (?, ?, ?, ?, ?, datetime('now'))`)
      payInsStmt.run(id, payment_date, payment_method || 'bank_transfer', payment_reference || '', notes || '')
    }

    // Get updated invoice
    const updatedInvoice = db.prepare(`SELECT i.*, c.name as client_name
         FROM invoices i
         LEFT JOIN clients c ON i.client_id = c.id
         WHERE i.id = ?`).get(id)

    res.json({
      message: 'Payment status updated successfully',
      invoice: updatedInvoice
    })

  } catch (error) {
    console.error('Error updating payment status:', error)
    res.status(500).json({ error: 'Failed to update payment status' })
  }
})

// Get invoice payment history
router.get('/:id/payments', async (req, res) => {
  try {
    const { id } = req.params
    const db = getDatabase()

    const payments = db.prepare(`SELECT * FROM invoice_payments
         WHERE invoice_id = ?
         ORDER BY payment_date DESC`).all(id)

    res.json(payments)

  } catch (error) {
    console.error('Error getting payment history:', error)
    res.status(500).json({ error: 'Failed to get payment history' })
  }
})

export default router
