# Cal<PERSON>ren Personal Website

A modern Vue.js website for <PERSON><PERSON>'s fractional tech operations business, featuring integrated timesheet management and invoice automation.

## 🚀 Features

- **Professional Website**: Modern, responsive design showcasing services and expertise
- **Timesheet Management**: Easy time entry with integration to existing CSV workflow
- **Invoice Automation**: Generate professional invoices using existing Python scripts
- **Client Management**: Simple client database and project tracking
- **Dashboard**: Overview of hours, revenue, and recent activity

## 🛠 Tech Stack

- **Frontend**: Vue 3 + Vite + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express + SQLite
- **Linting**: Oxylint
- **Deployment**: Docker + Docker Compose
- **Integration**: Existing Python automation scripts

## 📦 Quick Start

### Development

1. **Install dependencies**:
   ```bash
   npm install
   cd api && npm install && cd ..
   ```

2. **Start development servers**:
   ```bash
   # Start both frontend and API in development mode
   docker-compose --profile dev up
   
   # Or run separately:
   npm run dev          # Frontend on http://localhost:3000
   cd api && npm run dev # API on http://localhost:3001
   ```

### Production

1. **Build and run with Docker**:
   ```bash
   docker-compose up -d
   ```

2. **Access the website**:
   - Website: http://localhost
   - API: http://localhost:3001

## 🔧 Configuration

### Environment Variables

Create `.env` files as needed:

```bash
# Frontend (.env)
VITE_API_URL=http://localhost:3001

# Backend (api/.env)
NODE_ENV=production
PORT=3001
DATABASE_URL=sqlite:///app/data/calmren.db
```

### Integration with Python Service

The API triggers a containerized Python service to generate invoices and statements directly from the SQLite database (no CSV dependency). This ensures the generated HTML reflects your latest data.

- Invoice generation: API -> python-service `/invoices/generate`
- Outputs: HTML files in `invoices/` and `statements/` (ignored by Git except for `.gitkeep`)

## 📁 Project Structure

```
Calmren/
├── src/                    # Vue.js frontend
│   ├── components/         # Reusable components
│   ├── views/             # Page components
│   └── router/            # Vue Router config
├── api/                   # Express.js backend
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── database/      # Database setup & SQLite
│   │   └── scripts/       # Data import/export scripts
├── scripts/               # Legacy Python utilities (not used in production; excluded by .gitignore)
│   ├── invoice_generator.py    # Legacy generator (use python-service instead)
│   └── statement_generator.py  # Legacy generator (use python-service instead)
├── python-service/        # Docker Python service
├── invoices/              # Generated invoice files
├── statements/            # Generated statement files
├── archive/               # Legacy files and documentation
├── working-files/         # Additional documentation
├── DOCUMENTATION.md       # Complete system documentation
└── docker-compose.yml     # Container orchestration
```

## 🎯 Next Steps

1. **Complete the API routes** for timesheet and invoice management
2. **Add authentication** for secure access
3. **Deploy to production** (Cloudflare Pages + API)
4. **Integrate with existing automation** scripts

## 📧 Contact

Carleen Chamberlain  
Email: <EMAIL>  
Website: calmren.com
