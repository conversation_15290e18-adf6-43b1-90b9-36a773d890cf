# Legacy Content (Local Only)

The following directories contain historical or working materials. They are kept locally for reference but are excluded from the initial commit and remote pushes via `.gitignore`.

- `archive/` — legacy data, docs, scripts, spreadsheets
- `working-files/` — in-progress notes and artifacts
- `FSU_IT_Docs/` — client documentation (historical)
- `FSU_IT_Project/` — client project assets (historical)
- `scripts/` — legacy Python utilities (replaced by `python-service/`)

Why exclude:
- Contains sensitive or client-specific information
- Large, non-source assets not suitable for version control
- Superseded by the containerized Python service and the current API/Frontend

How to include specific files later (if needed):
1. Move or copy only the necessary files into a tracked location (e.g., `docs/`)
2. Or adjust `.gitignore` to unignore a subpath:
   - Add an exception rule like `!archive/README.md`

Note: project output folders (`invoices/`, `statements/`, and `data/`) are also ignored by default to avoid committing generated files and databases. `.gitkeep` markers are added so the folders exist in the repository structure.

